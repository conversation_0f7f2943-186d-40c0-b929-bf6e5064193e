import { type TBaseComponentProps } from '@ninebot/core'
import type { GetCategoriesQuery } from '@ninebot/core/src/graphql/generated/graphql'

import { CategoryList } from './components'

type TCategories = NonNullable<GetCategoriesQuery['categories']>
type TCategoryItems = NonNullable<TCategories['items']>
// 分类类型
type TCategoryItem = NonNullable<TCategoryItems[number]>

export type TCategoryPageProps = TBaseComponentProps<{
  data: TCategoryItem
}>

/**
 * 分类页面
 * 该页面名称/banner区域需要SSR渲染，以下部分可CSR渲染
 */
const CategoryPage = ({ data }: TCategoryPageProps) => {
  const { uid } = data

  return (
    <div className="min-h-screen bg-white">
      <CategoryList uid={uid} />
    </div>
  )
}

export default CategoryPage
