import {
  BannerCardSkeleton,
  ProductCardSkeleton,
  Skeleton,
} from '@/components';

import styles from './styles';

/**
 * CategorySkeleton 组件，用于显示普通分类页的骨架屏。
 * @returns {JSX.Element} - 返回一个包含骨架屏的视图。
 */
const CategorySkeleton = () => {
  const skeletonData = Array.from({ length: 4 }, (_, index) => ({
    id: index,
  }))

  return (
    <div className={styles.skeletonContainer}>
      <div className={styles.skeletonList}>
        {skeletonData.map((item) => (
          <Skeleton key={item.id} style={styles.skeletonItem} />
        ))}
      </div>
      <div className={styles.filterSkeleton}>
        <Skeleton style={styles.filterSkeletonItem} />
        <Skeleton style={styles.filterSkeletonItem} />
      </div>
      <BannerCardSkeleton />
      <ProductCardSkeleton length={6} />
    </div>
  );
};

export default CategorySkeleton;
