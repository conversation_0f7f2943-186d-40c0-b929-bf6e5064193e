import { RenderHtml, type TBaseComponentProps } from '@ninebot/core'
import type { GetCmsPageQuery } from '@ninebot/core/src/graphql/generated/graphql'

import { CustomEmpty, CustomNavBar } from '@/components'

import './style.css'

export type TCmsPageProps = TBaseComponentProps<{
  data: GetCmsPageQuery
}>

/**
 * CMS 页面
 * 该页面需要SSR渲染。
 */
const CmsPage = ({ data }: TCmsPageProps) => {
  const { cmsPage } = data

  return (
    <div className="regulation-component flex flex-1 flex-col bg-white">
      <CustomNavBar title={cmsPage?.title || ''} />
      {cmsPage?.content ? (
        <div className="regulation-content flex flex-1 bg-gray-base px-[16px]">
          <RenderHtml content={cmsPage.content} width={375} />
        </div>
      ) : (
        <div className="flex flex-1 flex-col bg-white pt-40">
          <CustomEmpty />
        </div>
      )}
    </div>
  )
}

export default CmsPage
