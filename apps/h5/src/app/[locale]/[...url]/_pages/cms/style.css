/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

/* 容器样式 */
.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 20px;
}

/* 隐私政策组件样式 */
.regulation-component {
  color: #000;
}

.regulation-title {
  font-size: 40px;
  line-height: 48px;
  padding: 80px 0 40px;
  text-align: center;
  font-weight: 600;
}

.regulation-content {
  max-width: 1440px;
  margin: 0 auto;
}

.body-item {
  border-bottom: 1px solid #d1d1d4;
  padding: 0 0 36px;
  margin-bottom: 36px;
}

.body-item:last-child {
  border-bottom: none;
}

.body-item .title {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
  padding: 26px 0 20px;
  color: #000;
}

.regulation-term-description {
  color: #222223;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
  margin-bottom: 16px;
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

table td {
  border: 1px solid #ccc;
  padding: 12px;
  vertical-align: top;
}

table td:first-child {
  text-align: center;
  font-weight: bold;
}

/* 链接样式 */
.container a {
  color: #2174d1;
  text-decoration: underline;
}

.container a:hover {
  text-decoration: none;
}

/* 粗体文本 */
.container b,
.container strong {
  font-weight: 600;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .regulation-title {
    font-size: 28px;
    line-height: 1.2;
    padding: 40px 0 30px;
  }

  .body-item .title {
    font-size: 20px;
    padding: 20px 0 15px;
  }

  .regulation-term-description {
    font-size: 14px;
  }

  table td {
    padding: 8px;
    font-size: 14px;
  }
}
