'use client'

import { Button } from 'antd'

import { useMediaQuery } from '@/hooks'

import { useProduct } from '../../context/ProductContext'
import QuantitySelector from '../ProductInfo/QuantitySelector'

interface productStatusType {
  isEverythingOutOfStock: boolean
  isOutOfStock: boolean
}

const ProductActionButtons = () => {
  const { onAddToCartBefore, isBuyNow, addCartLoading, buyNowLoading, productStatus, addToCartRateLimit, buyNowRateLimit } =
    useProduct() as {
      onAddToCartBefore: (quantity: number) => void
      isBuyNow: boolean
      addCartLoading: boolean
      buyNowLoading: boolean
      productStatus: productStatusType
      addToCartRateLimit: any
      buyNowRateLimit: any
    }

  const getStatusStock = () => {
    return !(productStatus.isEverythingOutOfStock || productStatus.isOutOfStock)
  }

  const responsive = useMediaQuery()

  return (
    <div className="flex w-full items-center gap-base-12">
      {/* 数量选择 */}
      <div>
        <QuantitySelector />
      </div>

      <div className="flex flex-1 items-center gap-base">
        {getStatusStock() ? (
          isBuyNow ? (
            <Button
              onClick={() => onAddToCartBefore(2)}
              disabled={addCartLoading || buyNowLoading || buyNowRateLimit?.isDisabled}
              size={responsive?.['xll'] ? 'large' : 'middle'}
              type="primary"
              style={{
                flex: 1,
              }}>
              {buyNowRateLimit?.buttonText || '立即购买'}
            </Button>
          ) : (
            <>
              <Button
                onClick={() => onAddToCartBefore(1)}
                disabled={addCartLoading || buyNowLoading || addToCartRateLimit?.isDisabled}
                size={responsive?.['xll'] ? 'large' : 'middle'}
                style={{
                  flex: 1,
                }}>
                {addToCartRateLimit?.buttonText || '加入购物车'}
              </Button>
              <Button
                onClick={() => onAddToCartBefore(2)}
                disabled={addCartLoading || buyNowLoading || buyNowRateLimit?.isDisabled}
                type="primary"
                size={responsive?.['xll'] ? 'large' : 'middle'}
                style={{
                  flex: 1,
                }}>
                {buyNowRateLimit?.buttonText || '立即购买'}
              </Button>
            </>
          )
        ) : (
          <Button
            disabled
            size={responsive?.['xll'] ? 'large' : 'middle'}
            style={{
              flex: 1,
            }}>
            售罄
          </Button>
        )}
      </div>
    </div>
  )
}

export default ProductActionButtons
