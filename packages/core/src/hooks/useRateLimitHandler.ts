'use client'

import { useCallback, useEffect, useState } from 'react'
import { useToastContext } from '../components/toast'
import { resolveCatchMessage, TCatchMessage, TRateLimitError } from '../utils'

/**
 * 限流处理模式
 */
export type TRateLimitMode = 'button' | 'toast'

/**
 * 限流处理配置
 */
export type TRateLimitConfig = {
  mode: TRateLimitMode
  originalText?: string // 按钮原始文案
}

/**
 * 限流处理结果
 */
export type TRateLimitResult = {
  isRateLimited: boolean
  countdown: number
  buttonText: string
  isDisabled: boolean
}

/**
 * 限流处理Hook
 */
export const useRateLimitHandler = (config: TRateLimitConfig): TRateLimitResult & {
  handleError: (error: TCatchMessage) => boolean
  reset: () => void
} => {
  const { mode, originalText = '' } = config
  const toast = useToastContext()
  
  const [isRateLimited, setIsRateLimited] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null)

  /**
   * 清理定时器
   */
  const clearTimer = useCallback(() => {
    if (intervalId) {
      clearInterval(intervalId)
      setIntervalId(null)
    }
  }, [intervalId])

  /**
   * 开始倒计时
   */
  const startCountdown = useCallback((retryMs: number) => {
    setIsRateLimited(true)
    setCountdown(Math.ceil(retryMs / 1000))
    
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setIsRateLimited(false)
          clearInterval(timer)
          setIntervalId(null)
          return 0
        }
        return prev - 1
      })
    }, 1000)
    
    setIntervalId(timer)
  }, [])

  /**
   * 处理错误
   */
  const handleError = useCallback((error: TCatchMessage): boolean => {
    const errorResult = resolveCatchMessage(error)
    
    // 检查是否为限流错误
    if (typeof errorResult === 'object' && errorResult.isRateLimit) {
      const rateLimitError = errorResult as TRateLimitError
      
      if (mode === 'toast') {
        // Toast模式：显示toast消息
        toast.show({
          icon: 'info',
          content: rateLimitError.message,
          duration: rateLimitError.retryMs,
        })
      } else {
        // Button模式：开始倒计时
        startCountdown(rateLimitError.retryMs)
      }
      
      return true // 表示已处理限流错误
    }
    
    return false // 表示不是限流错误，需要其他处理
  }, [mode, toast, startCountdown])

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    clearTimer()
    setIsRateLimited(false)
    setCountdown(0)
  }, [clearTimer])

  /**
   * 生成按钮文案
   */
  const buttonText = isRateLimited && mode === 'button' 
    ? `${originalText}（${countdown}s）`
    : originalText

  /**
   * 组件卸载时清理定时器
   */
  useEffect(() => {
    return () => {
      clearTimer()
    }
  }, [clearTimer])

  return {
    isRateLimited,
    countdown,
    buttonText,
    isDisabled: isRateLimited && mode === 'button',
    handleError,
    reset,
  }
}

export default useRateLimitHandler
